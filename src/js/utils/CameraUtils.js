/**
 * CameraUtils - Utility functions for camera positioning and bounding box calculations
 */

import * as THREE from 'three';

/**
 * Calculate the bounding box that encompasses all celestial bodies and their orbits
 */
export function calculateSolarSystemBounds(celestialBodies, includeOrbits = true) {
  const bounds = {
    min: new THREE.Vector3(Infinity, Infinity, Infinity),
    max: new THREE.Vector3(-Infinity, -Infinity, -Infinity)
  };

  // Track the maximum orbital radius for orbit inclusion
  let maxOrbitalRadius = 0;

  celestialBodies.forEach(body => {
    const position = body.getPosition();
    const radius = body.getRadius();

    // Expand bounds to include the body's position plus its radius
    bounds.min.x = Math.min(bounds.min.x, position.x - radius);
    bounds.min.y = Math.min(bounds.min.y, position.y - radius);
    bounds.min.z = Math.min(bounds.min.z, position.z - radius);

    bounds.max.x = Math.max(bounds.max.x, position.x + radius);
    bounds.max.y = Math.max(bounds.max.y, position.y + radius);
    bounds.max.z = Math.max(bounds.max.z, position.z + radius);

    // Track orbital radius for orbit bounds
    if (includeOrbits && body.name !== 'sun') {
      const orbitalRadius = body.semiMajorAxis || 0;
      maxOrbitalRadius = Math.max(maxOrbitalRadius, orbitalRadius);
    }
  });

  // If including orbits, expand bounds to encompass the largest orbit
  if (includeOrbits && maxOrbitalRadius > 0) {
    const orbitPadding = maxOrbitalRadius * 1.1; // 10% padding
    bounds.min.x = Math.min(bounds.min.x, -orbitPadding);
    bounds.min.y = Math.min(bounds.min.y, -orbitPadding);
    bounds.min.z = Math.min(bounds.min.z, -orbitPadding);

    bounds.max.x = Math.max(bounds.max.x, orbitPadding);
    bounds.max.y = Math.max(bounds.max.y, orbitPadding);
    bounds.max.z = Math.max(bounds.max.z, orbitPadding);
  }

  // Ensure bounds are reasonable (not infinite or too small)
  if (!isFinite(bounds.min.x) || !isFinite(bounds.max.x)) {
    console.warn('⚠️ Invalid bounds detected, using fallback');
    bounds.min.set(-500, -500, -500);
    bounds.max.set(500, 500, 500);
  }

  // Calculate center and size
  const center = new THREE.Vector3(
    (bounds.min.x + bounds.max.x) / 2,
    (bounds.min.y + bounds.max.y) / 2,
    (bounds.min.z + bounds.max.z) / 2
  );

  const size = new THREE.Vector3(
    bounds.max.x - bounds.min.x,
    bounds.max.y - bounds.min.y,
    bounds.max.z - bounds.min.z
  );

  const maxDimension = Math.max(size.x, size.y, size.z);

  console.log('📊 Solar system bounds calculated:', {
    center: center.toArray().map(v => v.toFixed(2)),
    size: size.toArray().map(v => v.toFixed(2)),
    maxDimension: maxDimension.toFixed(2),
    maxOrbitalRadius: maxOrbitalRadius.toFixed(2),
    bodyCount: celestialBodies.length
  });

  return {
    min: bounds.min,
    max: bounds.max,
    center,
    size,
    maxDimension,
    maxOrbitalRadius
  };
}

/**
 * Calculate optimal camera position to fit the entire solar system in view
 */
export function calculateOptimalCameraPosition(bounds, camera, padding = 1.2) {
  const { center, maxDimension } = bounds;

  console.log('🔍 Camera positioning calculation:', {
    center: center.toArray().map(v => v.toFixed(2)),
    maxDimension: maxDimension.toFixed(2),
    bounds: {
      min: bounds.min.toArray().map(v => v.toFixed(2)),
      max: bounds.max.toArray().map(v => v.toFixed(2))
    }
  });

  // Calculate distance needed to fit the entire scene
  const fov = (camera.fov || 75) * Math.PI / 180; // Convert to radians, default to 75 degrees
  const distance = (maxDimension / 2) / Math.tan(fov / 2) * padding;

  // For solar system, use a more conservative approach
  // Position camera at a reasonable distance with a slight elevation
  const minDistance = Math.max(maxDimension * 0.8, 100); // Ensure minimum distance
  const finalDistance = Math.max(distance, minDistance);

  // Use a smaller elevation angle for better view of the solar system
  const elevationAngle = Math.PI / 8; // 22.5 degrees instead of 45
  const horizontalDistance = finalDistance * Math.cos(elevationAngle);
  const verticalDistance = finalDistance * Math.sin(elevationAngle);

  // Position camera to get a good overview of the solar system
  const cameraPosition = new THREE.Vector3(
    center.x + horizontalDistance * 0.8, // Slightly offset for perspective
    center.y + verticalDistance,
    center.z + horizontalDistance * 0.6  // Less offset in Z for better view
  );

  console.log('📹 Calculated camera position:', {
    position: cameraPosition.toArray().map(v => v.toFixed(2)),
    lookAt: center.toArray().map(v => v.toFixed(2)),
    distance: finalDistance.toFixed(2),
    fov: camera.fov || 75
  });

  return {
    position: cameraPosition,
    lookAt: center,
    distance: finalDistance,
    bounds
  };
}

/**
 * Smoothly animate camera to a target position and orientation
 */
export class CameraAnimator {
  constructor(camera, navigationControls = null) {
    this.camera = camera;
    this.navigationControls = navigationControls;
    this.isAnimating = false;
    this.animationId = null;
    this.onComplete = null;
    this.onProgress = null;

    // Animation parameters
    this.duration = 2000; // 2 seconds default
    this.easing = this.easeInOutCubic;
  }

  /**
   * Animate camera to target position and look-at point
   */
  animateTo(targetPosition, targetLookAt, duration = 2000, onComplete = null, onProgress = null) {
    // Cancel any existing animation
    this.stop();

    this.isAnimating = true;
    this.duration = duration;
    this.onComplete = onComplete;
    this.onProgress = onProgress;

    // Store initial state
    const startPosition = this.camera.position.clone();
    const startQuaternion = this.camera.quaternion.clone();

    // Calculate target quaternion by looking at the target
    const tempCamera = new THREE.PerspectiveCamera();
    tempCamera.position.copy(targetPosition);
    tempCamera.lookAt(targetLookAt);
    const targetQuaternion = tempCamera.quaternion.clone();

    // Pause navigation controls during animation
    if (this.navigationControls) {
      this.navigationControls.pause();
    }

    const startTime = performance.now();

    const animate = (currentTime) => {
      if (!this.isAnimating) return;

      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / this.duration, 1);
      const easedProgress = this.easing(progress);

      // Interpolate position
      this.camera.position.lerpVectors(startPosition, targetPosition, easedProgress);

      // Interpolate rotation
      this.camera.quaternion.slerpQuaternions(startQuaternion, targetQuaternion, easedProgress);

      // Call progress callback
      if (this.onProgress) {
        this.onProgress(progress);
      }

      if (progress >= 1) {
        // Animation complete
        this.stop();
        
        // Sync navigation controls with final camera state
        if (this.navigationControls && this.navigationControls.syncEulerWithCamera) {
          this.navigationControls.syncEulerWithCamera();
        }

        if (this.onComplete) {
          this.onComplete();
        }
      } else {
        this.animationId = requestAnimationFrame(animate);
      }
    };

    this.animationId = requestAnimationFrame(animate);
  }

  /**
   * Stop the current animation
   */
  stop() {
    this.isAnimating = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }

    // Resume navigation controls
    if (this.navigationControls) {
      this.navigationControls.resume();
    }
  }

  /**
   * Ease-in-out cubic easing function
   */
  easeInOutCubic(t) {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  }

  /**
   * Check if animation is currently running
   */
  isRunning() {
    return this.isAnimating;
  }
}

/**
 * Get the furthest celestial body from the center for bounds calculation
 */
export function getFurthestBody(celestialBodies) {
  let furthestDistance = 0;
  let furthestBody = null;

  celestialBodies.forEach(body => {
    if (body.name === 'sun') return; // Skip the sun

    const position = body.getPosition();
    const distance = position.length(); // Distance from origin (sun)

    if (distance > furthestDistance) {
      furthestDistance = distance;
      furthestBody = body;
    }
  });

  return { body: furthestBody, distance: furthestDistance };
}

/**
 * Calculate camera position for different viewing modes
 */
export function calculateViewModePosition(bounds, mode = 'overview') {
  const { center, maxDimension } = bounds;

  switch (mode) {
    case 'overview':
      // Standard overview position
      return calculateOptimalCameraPosition(bounds, { fov: 75 }, 1.2);

    case 'wide':
      // Wider view with more padding
      return calculateOptimalCameraPosition(bounds, { fov: 75 }, 1.8);

    case 'tight':
      // Closer view with less padding
      return calculateOptimalCameraPosition(bounds, { fov: 75 }, 1.0);

    case 'top':
      // Top-down view
      return {
        position: new THREE.Vector3(center.x, center.y + maxDimension, center.z),
        lookAt: center
      };

    case 'side':
      // Side view
      return {
        position: new THREE.Vector3(center.x + maxDimension, center.y, center.z),
        lookAt: center
      };

    default:
      return calculateOptimalCameraPosition(bounds, { fov: 75 }, 1.2);
  }
}
